<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpiderWeb | Professional Web Design & Development</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #0053bc;
            --secondary: #ffffff;
            --dark: #001a3d;
            --light: #f8faff;
            --web-strand: #cce0ff;
            --web-highlight: #4d9fff;
            --web-shadow: #003d8a;
            --spider-body: #0053bc;
            --spider-leg: #003d8a;
            --spider-eye: #fff;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background:
                radial-gradient(rgba(0,26,61,0.78), #000a1a 85%),
                linear-gradient(135deg, #001a3d 0%, #003d8a 100%) fixed;
            color: var(--light);
            overflow-x: hidden;
        }
        .web-bg {
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            z-index: 0;
            pointer-events: none;
            mix-blend-mode: lighten;
        }
        .realistic-web {
            filter: drop-shadow(0 0 8px #0053bc66);
        }
        .animated-strand {
            stroke-dasharray: 1800;
            stroke-dashoffset: 1800;
            animation: draw-web-anim 2.8s cubic-bezier(0.64,0.13,0.23,0.97) forwards;
        }
        @keyframes draw-web-anim {
            to { stroke-dashoffset: 0;}
        }
        .fade-in {
            animation: fadeIn 1s cubic-bezier(0.42,0,0.58,1) forwards;
            opacity: 0;
        }
        @keyframes fadeIn {
            to { opacity: 1;}
        }
        .spider-hero {
            animation: heroFloat 6s ease-in-out infinite;
        }
        @keyframes heroFloat {
            0% { transform: translateY(0);}
            50% { transform: translateY(-22px);}
            100% { transform: translateY(0);}
        }
        .spider-crawl {
            animation: spiderCrawl 2.5s cubic-bezier(0.6,0.2,0.1,0.95) infinite alternate;
            transform-origin: 50% 90%;
        }
        @keyframes spiderCrawl {
            from { transform: rotate(-7deg); }
            to { transform: rotate(8deg);}
        }
        .web-section {
            position: relative;
            z-index: 2;
        }
        .spider-btn {
            color: var(--secondary);
            background: var(--spider-body);
            border: 2px solid var(--web-highlight);
            transition: background 0.3s, color 0.3s;
            box-shadow: 0 2px 16px #0053bc22;
        }
        .spider-btn:hover {
            background: var(--secondary);
            color: var(--primary);
        }
        /* Strands connecting hero call-to-action */
        .hero-strands svg {
            position: absolute;
            z-index: 10;
            pointer-events: none;
        }
        /* utility */
        .scrollbar-hide::-webkit-scrollbar { display:none; }
        .scrollbar-hide { -ms-overflow-style: none; scrollbar-width: none;}
    </style>
</head>
<body class="font-sans antialiased relative">
    <!-- Improved SPIDERWEB BACKGROUND SVG, much more realistic: -->
    <svg class="web-bg realistic-web" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
        <!-- Central large web, highly detailed & drawn with randomness for realism -->
        <g id="center-web">
            <!-- Draw main web circles (random line variations for realism) -->
            <circle cx="960" cy="540" r="440" stroke="var(--web-strand)" stroke-width="1.9" opacity="0.21" class="animated-strand"/>
            <ellipse cx="960" cy="540" rx="355" ry="350" stroke="var(--web-strand)" stroke-width="1.6" opacity="0.26" class="animated-strand"/>
            <ellipse cx="960" cy="540" rx="250" ry="245" stroke="var(--web-strand)" stroke-width="1.1" opacity="0.16" class="animated-strand"/>
            <ellipse cx="960" cy="540" rx="150" ry="148" stroke="var(--web-highlight)" stroke-width="0.9" opacity="0.09" class="animated-strand"/>
            <ellipse cx="960" cy="540" rx="60" ry="65" stroke="var(--web-highlight)" stroke-width="1.2" opacity="0.07" class="animated-strand"/>
            <!-- Radials (irregular for realism) -->
            <g stroke="var(--web-strand)" stroke-width="1.2" opacity="0.7" class="animated-strand">
                <line x1="960" y1="540" x2="960" y2="90"/>
                <line x1="960" y1="540" x2="1433" y2="220"/>
                <line x1="960" y1="540" x2="1825" y2="540"/>
                <line x1="960" y1="540" x2="1453" y2="860"/>
                <line x1="960" y1="540" x2="960" y2="1020"/>
                <line x1="960" y1="540" x2="444" y2="860"/>
                <line x1="960" y1="540" x2="98" y2="540"/>
                <line x1="960" y1="540" x2="455" y2="220"/>
                <line x1="960" y1="540" x2="1245" y2="76"/>
                <line x1="960" y1="540" x2="1675" y2="340"/>
                <line x1="960" y1="540" x2="1682" y2="749"/>
                <line x1="960" y1="540" x2="604" y2="900"/>
                <line x1="960" y1="540" x2="318" y2="372"/>
            </g>
            <!-- Random silk connecting lines for realism (using bezier curves) -->
            <path d="M340 420 Q960 320 1580 415" stroke="var(--web-highlight)" stroke-width="0.9" opacity="0.09" fill="none"/>
            <path d="M320 650 Q960 720 1610 655" stroke="var(--web-highlight)" stroke-width="0.9" opacity="0.09" fill="none"/>
            <path d="M415 270 Q960 540 1450 320" stroke="var(--web-highlight)" stroke-width="0.7" opacity="0.08" fill="none"/>
            <path d="M475 840 Q960 540 1400 860" stroke="var(--web-highlight)" stroke-width="0.8" opacity="0.07" fill="none"/>
            <!-- Center shadow -->
            <ellipse cx="960" cy="600" rx="600" ry="55" fill="var(--web-shadow)" opacity="0.030"/>
        </g>
        <!-- Corners, miniwebs -->
        <g id="corner-webs">
            <!-- Top left corner -->
            <g>
                <ellipse cx="0" cy="0" rx="175" ry="160" stroke="var(--web-strand)" stroke-width="1" opacity="0.13"/>
                <ellipse cx="0" cy="0" rx="105" ry="96" stroke="var(--web-highlight)" stroke-width="0.8" opacity="0.07"/>
                <g stroke="var(--web-strand)" stroke-width="0.8" opacity="0.14">
                    <line x1="0" y1="0" x2="178" y2="0"/>
                    <line x1="0" y1="0" x2="90" y2="160"/>
                    <line x1="0" y1="0" x2="0" y2="200"/>
                    <line x1="0" y1="0" x2="140" y2="115"/>
                </g>
            </g>
            <!-- Bottom right corner -->
            <g>
                <ellipse cx="1920" cy="1080" rx="150" ry="130" stroke="var(--web-strand)" stroke-width="1.2" opacity="0.09"/>
                <ellipse cx="1920" cy="1080" rx="65" ry="55" stroke="var(--web-highlight)" stroke-width="0.7" opacity="0.06"/>
                <g stroke="var(--web-strand)" stroke-width="0.8" opacity="0.13">
                    <line x1="1920" y1="1080" x2="1740" y2="990"/>
                    <line x1="1920" y1="1080" x2="1800" y2="1080"/>
                    <line x1="1920" y1="1080" x2="1920" y2="950"/>
                    <line x1="1920" y1="1080" x2="1805" y2="1048"/>
                </g>
            </g>
        </g>
    </svg>
    <!-- Header/Navigation -->
    <header class="web-section z-30 relative" style="background:rgba(0,26,61,0.93)">
        <div class="container mx-auto px-6 py-4 flex items-center justify-between">
            <div class="flex items-center gap-2">
                <!-- SPYDERWEB LOGO: New static logo image -->
                <img src="images/SPYDERWEB.png" alt="SpiderWeb logo" class="h-14 w-auto mr-2" style="filter: brightness(1.1);">
                <h1 class="text-2xl font-black tracking-wide text-white drop-shadow-lg select-none">SpiderWeb</h1>
            </div>
            <nav class="hidden md:block">
                <ul class="flex gap-8 text-lg">
                    <li><a href="#services" class="hover:text-blue-200 transition">Web Services</a></li>
                    <li><a href="#process" class="hover:text-blue-200 transition">Process</a></li>
                    <li><a href="#portfolio" class="hover:text-blue-200 transition">Portfolio</a></li>
                    <li><a href="#contact-form" class="spider-btn rounded-full px-5 py-2 font-semibold">Get Caught</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- HERO Section -->
    <section class="web-section pt-20 pb-12 relative">
        <div class="container mx-auto px-6 relative z-10 flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 mb-14 md:mb-0 relative z-10">
                <h2 class="text-4xl md:text-5xl font-black leading-tight mb-5 fade-in" style="animation-delay:0.05s">
                    <span class="inline-flex flex-col relative">
                        <span>Ensnare More Clients</span>
                        <svg width="160" height="22" viewBox="0 0 180 28" fill="none" class="absolute -bottom-4 left-0">
                            <path d="M1 18 Q30 5 80 21 Q120 33 178 10" stroke="#4d9fff" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="7 9"/>
                        </svg>
                    </span>
                    <br>
                    <span class="text-blue-300">With an Irresistible <span class="font-black text-white">Web</span></span>
                </h2>
                <p class="text-xl mb-9 text-blue-200 fade-in" style="animation-delay:0.18s">
                    We spin digital webs that attract, capture, and convert prey (clients!) for your business.
                </p>
                <div class="hero-strands absolute left-5 top-2 md:top-20 pointer-events-none">
                    <svg width="100" height="120" viewBox="0 0 100 120">
                        <path d="M30 0 Q50 40 25 118" stroke="#4d9fff" stroke-width="1.5" fill="none" opacity="0.14"/>
                        <path d="M55 5 Q77 50 75 118" stroke="#4d9fff" stroke-width="1.1" fill="none" opacity="0.1"/>
                    </svg>
                </div>
                <div class="flex flex-wrap gap-6 fade-in" style="animation-delay:0.27s">
                    <a href="#contact-form" class="spider-btn px-8 py-4 rounded-full font-bold shadow-lg">Spin a Web for Me</a>
                    <a href="#services" class="border-2 border-blue-200 px-8 py-4 rounded-full font-bold hover:bg-white hover:bg-opacity-5 transition">Web Services</a>
                </div>
            </div>
            <!-- Orange Spider on a web thread (animated) + site preview -->
            <div class="md:w-1/2 relative flex items-center justify-center min-h-[340px] select-none">
                <div class="absolute left-1/2 top-0 z-20 -translate-x-1/2 pointer-events-none">
                    <!-- Hanging, cute orange spider, using improved SVG form -->
                    <svg width="83" height="182" viewBox="0 0 70 182" fill="none" class="spider-hero z-30">
                        <!-- Web thread -->
                        <line x1="35" y1="0" x2="35" y2="41" stroke="#4d9fff" stroke-width="3"/>
                        <!-- Blue spider body, true shape -->
                        <g class="spider-crawl">
                            <ellipse cx="35" cy="90" rx="15" ry="22" fill="#0053bc" stroke="#003d8a" stroke-width="2.3"/>
                            <ellipse cx="35" cy="69" rx="8" ry="8.7" fill="#0053bc" stroke="#003d8a" stroke-width="1.5"/>
                            <!-- Eyes -->
                            <ellipse cx="32.5" cy="67.5" rx="1.1" ry="1.5" fill="#fff"/>
                            <ellipse cx="37.5" cy="67.5" rx="1.1" ry="1.5" fill="#fff"/>
                            <!-- Legs: realistic 8 curved legs, left & right -->
                            <!-- Left -->
                            <path d="M19 76 Q10 54 31 70" stroke="#003d8a" stroke-width="1.9" stroke-linecap="round"/>
                            <path d="M18 91 Q2 95 25 87" stroke="#003d8a" stroke-width="1.47" stroke-linecap="round"/>
                            <path d="M21 107 Q10 120 33 100" stroke="#003d8a" stroke-width="1.13" stroke-linecap="round"/>
                            <path d="M26 120 Q17 139 36 104" stroke="#003d8a" stroke-width="1.07" stroke-linecap="round"/>
                            <!-- Right -->
                            <path d="M50 76 Q59 54 39 70" stroke="#003d8a" stroke-width="1.9" stroke-linecap="round"/>
                            <path d="M51 91 Q67 95 45 87" stroke="#003d8a" stroke-width="1.47" stroke-linecap="round"/>
                            <path d="M48 107 Q59 120 37 100" stroke="#003d8a" stroke-width="1.13" stroke-linecap="round"/>
                            <path d="M43 120 Q52 139 34 104" stroke="#003d8a" stroke-width="1.07" stroke-linecap="round"/>
                            <!-- Fangs -->
                            <path d="M33 78 Q32 81 32 84" stroke="#fff" stroke-width="0.7" stroke-linecap="round"/>
                            <path d="M37 78 Q38 81 38 84" stroke="#fff" stroke-width="0.7" stroke-linecap="round"/>
                        </g>
                    </svg>
                </div>
                <div class="bg-gradient-to-br from-blue-800/80 to-blue-900/60 rounded-2xl px-2 py-2 shadow-2xl max-w-md w-full z-10" style="border:2.5px solid #4d9fff66; position:relative;">
                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" alt="Website mockup" class="rounded-xl w-full h-auto" style="filter: drop-shadow(0 3px 13px #0053bc55);">
                    <!-- Overlayed subtle web -->
                    <svg viewBox="0 0 360 180" fill="none" class="absolute left-0 top-0 w-full h-full opacity-30 pointer-events-none">
                        <ellipse cx="180" cy="90" rx="170" ry="70" stroke="#4d9fff" stroke-width="2" opacity="0.14"/>
                        <ellipse cx="180" cy="90" rx="115" ry="37" stroke="#fff" stroke-width="1" opacity="0.09"/>
                    </svg>
                </div>
            </div>
        </div>
    </section>

    <!-- Client Form Section -->
    <section id="contact-form" class="web-section py-20 relative bg-[#001122]">
        <!-- Silk web decor on corners -->
        <svg class="absolute top-0 left-0 h-56 w-56 opacity-45" style="z-index:1" fill="none" viewBox="0 0 200 200">
            <circle cx="0" cy="0" r="60" stroke="#4d9fff" stroke-width="1.2"/>
            <circle cx="0" cy="0" r="40" stroke="#4d9fff" stroke-width="0.9" opacity="0.7"/>
            <circle cx="0" cy="0" r="20" stroke="#4d9fff" stroke-width="0.7" opacity="0.3"/>
            <line x1="0" y1="0" x2="100" y2="30" stroke="#4d9fff" stroke-width="1"/>
            <line x1="0" y1="0" x2="45" y2="115" stroke="#4d9fff" stroke-width="1"/>
        </svg>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-10">
                <h2 class="text-3xl md:text-4xl font-black text-white mb-3">Get Caught in Our Web</h2>
                <p class="text-lg text-blue-300 max-w-xl mx-auto fade-in" style="animation-delay:0.09s">Fill out the form if you wish to be our next juicy catch.<br>Our spiders will connect with you in 24h.</p>
            </div>

            <form id="projectForm" autocomplete="off" class="bg-[#001a3d]/95 rounded-2xl p-8 border border-blue-900 max-w-2xl mx-auto mb-4 web-section shadow-2xl fade-in" style="animation-delay:0.15s;">
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-blue-200 mb-5 border-b border-blue-700 pb-2">Your Details</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-blue-200 font-medium mb-1">Your Full Name*</label>
                            <input type="text" id="name" name="name" required class="w-full px-4 py-3 rounded-lg border border-blue-800 bg-[#003366] text-white focus:outline-none focus:border-blue-500 transition input-focus">
                        </div>
                        <div>
                            <label for="email" class="block text-blue-200 font-medium mb-1">Email Address*</label>
                            <input type="email" id="email" name="email" required class="w-full px-4 py-3 rounded-lg border border-blue-800 bg-[#003366] text-white focus:outline-none focus:border-blue-500 transition input-focus">
                        </div>
                        <div>
                            <label for="phone" class="block text-blue-200 font-medium mb-1">Phone Number*</label>
                            <input type="tel" id="phone" name="phone" required class="w-full px-4 py-3 rounded-lg border border-blue-800 bg-[#003366] text-white focus:outline-none focus:border-blue-500 transition input-focus">
                        </div>
                        <div>
                            <label for="business" class="block text-blue-200 font-medium mb-1">Business Name*</label>
                            <input type="text" id="business" name="business" required class="w-full px-4 py-3 rounded-lg border border-blue-800 bg-[#003366] text-white focus:outline-none focus:border-blue-500 transition input-focus">
                        </div>
                    </div>
                </div>

                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-blue-200 mb-5 border-b border-blue-700 pb-2">About Your Web</h3>
                    <div class="mb-6">
                        <label for="description" class="block text-blue-200 font-medium mb-1">Describe the web you want spun for your business*</label>
                        <textarea id="description" name="description" rows="4" required class="w-full px-4 py-3 rounded-lg border border-blue-800 bg-[#003366] text-white focus:outline-none focus:border-blue-500 transition input-focus" placeholder="Describe your goals, prey (target clients), and particular features..."></textarea>
                    </div>

                    <div class="mb-6">
                        <label class="block text-blue-200 font-medium mb-1">Type of prey you want to catch*</label>
                        <div class="space-y-2">
                            <label class="flex gap-2 items-center cursor-pointer hover:text-blue-200">
                                <input type="checkbox" name="services" value="Consulting" class="h-5 w-5 rounded border border-blue-700 text-blue-600 bg-[#003366] focus:ring-blue-500">
                                Consulting
                            </label>
                            <label class="flex gap-2 items-center cursor-pointer hover:text-blue-200">
                                <input type="checkbox" name="services" value="Professional Services" class="h-5 w-5 rounded border border-blue-700 text-blue-600 bg-[#003366] focus:ring-blue-500">
                                Professional Services
                            </label>
                            <label class="flex gap-2 items-center cursor-pointer hover:text-blue-200">
                                <input type="checkbox" name="services" value="Health & Wellness" class="h-5 w-5 rounded border border-blue-700 text-blue-600 bg-[#003366] focus:ring-blue-500">
                                Health & Wellness
                            </label>
                            <label class="flex gap-2 items-center cursor-pointer hover:text-blue-200">
                                <input type="checkbox" name="services" value="Home Services" class="h-5 w-5 rounded border border-blue-700 text-blue-600 bg-[#003366] focus:ring-blue-500">
                                Home Services
                            </label>
                            <label class="flex gap-2 items-center">
                                <input type="checkbox" name="services" value="Other" class="h-5 w-5 rounded border border-blue-700 text-blue-600 bg-[#003366] focus:ring-blue-500">
                                Other (specify in description)
                            </label>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label for="timeframe" class="block text-blue-200 font-medium mb-1">When do you want to start catching clients?*</label>
                        <select id="timeframe" name="timeframe" required class="w-full px-4 py-3 rounded-lg border border-blue-800 bg-[#003366] text-white focus:outline-none focus:border-blue-500 transition input-focus">
                            <option value="">Select timeframe</option>
                            <option value="1-2 weeks">1-2 weeks (rush web, extra silk!)</option>
                            <option value="2-4 weeks">2-4 weeks</option>
                            <option value="4-6 weeks">4-6 weeks</option>
                            <option value="6-8 weeks">6-8 weeks</option>
                            <option value="9+ weeks">9+ weeks (web can wait...)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-blue-200 font-medium mb-1">Upload your current web (optional)</label>
                        <input type="file" id="profile" name="profile" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.webp" class="file-input w-full px-4 py-3 rounded-lg border border-blue-800 bg-[#003366] text-white transition input-focus">
                        <p class="text-xs text-blue-400 mt-1">PDF, image, or doc that describes your business.</p>
                    </div>
                </div>

                <div class="flex items-center mb-6">
                    <input type="checkbox" id="agreement" name="agreement" required class="h-5 w-5 rounded border border-blue-700 text-blue-600 bg-[#003366] focus:ring-blue-500">
                    <label for="agreement" class="ml-2 text-blue-200 text-sm">I accept that I am the prey, and consent to being caught in the SpiderWeb for my benefit!</label>
                </div>

                <button type="submit" class="w-full spider-btn font-bold py-4 px-6 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 relative overflow-hidden group">
                    <span class="relative z-10">Submit and Surrender 🕸️</span>
                    <!-- Spider icon next to text -->
                    <svg class="absolute right-5 top-1/2 transform -translate-y-1/2 w-6 h-6 opacity-0 group-hover:opacity-100 transition-opacity" viewBox="0 0 32 32" fill="none">
                        <circle cx="16" cy="20" r="8" fill="#001a3d"/>
                        <ellipse cx="16" cy="12" rx="4" ry="4.8" fill="#001a3d"/>
                        <ellipse cx="14.2" cy="11.7" rx="0.7" ry="1" fill="#fff"/>
                        <ellipse cx="17.9" cy="11.7" rx="0.7" ry="1" fill="#fff"/>
                        <path d="M9 14 Q6 6 14 13" stroke="#000a1a" stroke-width="1.3" stroke-linecap="round"/>
                        <path d="M23 14 Q26 6 18 13" stroke="#000a1a" stroke-width="1.3" stroke-linecap="round"/>
                    </svg>
                </button>
            </form>

            <div id="successMessage" class="hidden mt-10 text-center bg-gradient-to-br from-blue-950/90 to-blue-900/90 border border-blue-700 p-10 rounded-2xl fade-in">
                <svg width="74" height="74" fill="none" class="mx-auto mb-6">
                    <circle cx="37" cy="37" r="35" stroke="#4d9fff" stroke-width="3"/>
                    <path d="M20 38 L34 51 L55 23" stroke="#0053bc" stroke-width="4.5" stroke-linecap="round"/>
                    <!-- Little spider legs -->
                    <path d="M12 35 q-4 -8 8 -13" stroke="#003d8a" stroke-width="2" />
                    <path d="M62 31 q7 -8 -6 -10" stroke="#003d8a" stroke-width="2" />
                </svg>
                <h3 class="text-2xl font-black text-blue-100 mb-2">Caught!</h3>
                <p class="text-blue-200 mb-6">
                    Our webs are strong and you've been ensnared.<br>We'll contact you ASAP to plan a new web irresistible to your prey.
                </p>
                <a href="#" class="spider-btn inline-block px-8 py-3 rounded-full">Return to Home</a>
            </div>
        </div>
        <!-- Bottom right silk web -->
        <svg class="absolute bottom-0 right-0 h-40 w-44 opacity-40 pointer-events-none" fill="none" viewBox="0 0 160 130">
            <ellipse cx="160" cy="130" rx="90" ry="29" stroke="#4d9fff" stroke-width="1.4"/>
            <ellipse cx="160" cy="130" rx="58" ry="15" stroke="#4d9fff" stroke-width="0.7"/>
            <line x1="160" y1="130" x2="120" y2="64" stroke="#4d9fff" stroke-width="1"/>
            <line x1="160" y1="130" x2="80" y2="95" stroke="#4d9fff" stroke-width="1"/>
        </svg>
    </section>

    <!-- SERVICES -->
    <section id="services" class="web-section py-20 relative bg-[#001a3d]">
        <svg class="absolute -top-10 right-0 opacity-25 pointer-events-none z-0" width="500" height="290" viewBox="0 0 500 290" fill="none">
            <ellipse cx="500" cy="180" rx="230" ry="50" stroke="#4d9fff" stroke-width="7" opacity="0.14"/>
            <ellipse cx="500" cy="280" rx="120" ry="24" stroke="#0053bc" stroke-width="6" opacity="0.12"/>
        </svg>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-black text-white mb-4">Our Web-Weaving Services</h2>
                <p class="text-lg text-blue-200 max-w-md mx-auto">We build webs so attractive, your prey will come <span class="text-white font-bold">willingly</span>.</p>
            </div>
            <div class="grid md:grid-cols-3 gap-7">
                <div class="bg-[#003366]/90 rounded-xl p-8 border-2 border-blue-900 shadow-lg relative group fade-in" style="animation-delay:0.08s">
                    <svg width="48" height="48" viewBox="0 0 48 48" class="absolute -top-5 right-3 z-10"><ellipse cx="24" cy="24" rx="17" ry="17" fill="#4d9fff33"/></svg>
                    <div class="bg-gradient-to-br from-blue-700 to-blue-900 text-white rounded-full w-14 h-14 flex items-center justify-center mb-5 shadow-lg">
                        <i class="fas fa-laptop-code text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Enticing Web Design</h3>
                    <p class="text-blue-100">Beautiful, spidery layouts that draw your prey into your site and keep them there.</p>
                </div>

                <div class="bg-[#003366]/90 rounded-xl p-8 border-2 border-blue-900 shadow-lg relative group fade-in" style="animation-delay:0.12s">
                    <div class="bg-gradient-to-br from-blue-700 to-blue-900 text-white rounded-full w-14 h-14 flex items-center justify-center mb-5 shadow-lg">
                        <i class="fas fa-mobile-alt text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Mobile Traps</h3>
                    <p class="text-blue-100">Sticky, responsive sites: clients can’t escape your web, no matter the device!</p>
                </div>

                <div class="bg-[#261b3d]/90 rounded-xl p-8 border-2 border-purple-900 shadow-lg relative group fade-in" style="animation-delay:0.16s">
                    <div class="bg-gradient-to-br from-purple-700 to-indigo-900 text-white rounded-full w-14 h-14 flex items-center justify-center mb-5 shadow-lg">
                        <i class="fas fa-search-dollar text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">SEO Silk</h3>
                    <p class="text-purple-100">Organic reach that reels in fresh prey from across the search engine web.</p>
                </div>

                <div class="bg-[#261b3d]/90 rounded-xl p-8 border-2 border-purple-900 shadow-lg relative group fade-in" style="animation-delay:0.2s">
                    <div class="bg-gradient-to-br from-purple-700 to-indigo-900 text-white rounded-full w-14 h-14 flex items-center justify-center mb-5 shadow-lg">
                        <i class="fas fa-tachometer-alt text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Lightning Webs</h3>
                    <p class="text-purple-100">Speed-optimized code ensures your prey gets stuck before they can run.</p>
                </div>

                <div class="bg-[#261b3d]/90 rounded-xl p-8 border-2 border-purple-900 shadow-lg relative group fade-in" style="animation-delay:0.25s">
                    <div class="bg-gradient-to-br from-purple-700 to-indigo-900 text-white rounded-full w-14 h-14 flex items-center justify-center mb-5 shadow-lg">
                        <i class="fas fa-edit text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Web Maintenance</h3>
                    <p class="text-purple-100">We tend and fortify your web, so it keeps snaring prey for you.</p>
                </div>

                <div class="bg-[#261b3d]/90 rounded-xl p-8 border-2 border-purple-900 shadow-lg relative group fade-in" style="animation-delay:0.3s">
                    <div class="bg-gradient-to-br from-purple-700 to-indigo-900 text-white rounded-full w-14 h-14 flex items-center justify-center mb-5 shadow-lg">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Conversion Webs</h3>
                    <p class="text-purple-100">Convincing layouts and copy wrap your prey up and <span class="text-orange-300 font-bold">get them to stay</span>.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- PROCESS Section -->
    <section id="process" class="web-section py-20 relative bg-[#191336]">
        <!-- Web radial background art -->
        <svg class="absolute w-full h-full opacity-10 pointer-events-none left-0 top-0 z-0" viewBox="0 0 1000 400"><circle cx="500" cy="200" r="170" stroke="#be84fc" stroke-width="2"/><circle cx="500" cy="200" r="117" stroke="#be84fc" stroke-width="1.3"/></svg>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-14">
                <h2 class="text-3xl md:text-4xl font-black text-white mb-4">How We Spin Your Web</h2>
                <p class="text-lg text-purple-200 max-w-2xl mx-auto">Meticulous process: from strategy to a finished web crawling with clients.</p>
            </div>
            <ol class="relative border-l-4 border-purple-800 pl-8 space-y-14 max-w-2xl mx-auto fade-in" style="animation-delay:0.15s;">
                <li class="flex items-start gap-6">
                    <div class="flex-shrink-0">
                        <!-- Little spider icon beside each step! -->
                        <svg width="36" height="34" viewBox="0 0 38 33"><ellipse cx="19" cy="24" rx="7" ry="8" fill="#2a133f"/><ellipse cx="19" cy="15" rx="3" ry="3.7" fill="#2a133f"/><ellipse cx="17.8" cy="14.2" rx="0.5" ry="1" fill="#fff"/><ellipse cx="20.2" cy="14.2" rx="0.5" ry="1" fill="#fff"/><path d="M9 16 Q7 10 17 12" stroke="#08000f" stroke-width="1.3"/><path d="M29 16 Q31 10 21 12" stroke="#08000f" stroke-width="1.3"/></svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-purple-200 mb-1">Discovery</h3>
                        <p class="text-purple-100">We probe your business, sussing out the prey you need to catch.</p>
                    </div>
                </li>
                <li class="flex items-start gap-6">
                    <div class="flex-shrink-0">
                        <svg width="36" height="34" viewBox="0 0 38 33"><ellipse cx="19" cy="24" rx="7" ry="8" fill="#2a133f"/><ellipse cx="19" cy="15" rx="3" ry="3.7" fill="#2a133f"/><ellipse cx="17.8" cy="14.2" rx="0.5" ry="1" fill="#fff"/><ellipse cx="20.2" cy="14.2" rx="0.5" ry="1" fill="#fff"/><path d="M9 16 Q7 10 17 12" stroke="#08000f" stroke-width="1.3"/><path d="M29 16 Q31 10 21 12" stroke="#08000f" stroke-width="1.3"/></svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-purple-200 mb-1">Strategy & Planning</h3>
                        <p class="text-purple-100">Spiders map out the stickiest path to maximizing your prey catch.</p>
                    </div>
                </li>
                <li class="flex items-start gap-6">
                    <div class="flex-shrink-0">
                        <svg width="36" height="34" viewBox="0 0 38 33"><ellipse cx="19" cy="24" rx="7" ry="8" fill="#2a133f"/><ellipse cx="19" cy="15" rx="3" ry="3.7" fill="#2a133f"/><ellipse cx="17.8" cy="14.2" rx="0.5" ry="1" fill="#fff"/><ellipse cx="20.2" cy="14.2" rx="0.5" ry="1" fill="#fff"/><path d="M9 16 Q7 10 17 12" stroke="#08000f" stroke-width="1.3"/><path d="M29 16 Q31 10 21 12" stroke="#08000f" stroke-width="1.3"/></svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-purple-200 mb-1">Web Design</h3>
                        <p class="text-purple-100">We weave, layer by layer, a beautiful web tailored for your business and your prey.</p>
                    </div>
                </li>
                <li class="flex items-start gap-6">
                    <div class="flex-shrink-0">
                        <svg width="36" height="34" viewBox="0 0 38 33"><ellipse cx="19" cy="24" rx="7" ry="8" fill="#2a133f"/><ellipse cx="19" cy="15" rx="3" ry="3.7" fill="#2a133f"/><ellipse cx="17.8" cy="14.2" rx="0.5" ry="1" fill="#fff"/><ellipse cx="20.2" cy="14.2" rx="0.5" ry="1" fill="#fff"/><path d="M9 16 Q7 10 17 12" stroke="#08000f" stroke-width="1.3"/><path d="M29 16 Q31 10 21 12" stroke="#08000f" stroke-width="1.3"/></svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-purple-200 mb-1">Web Development</h3>
                        <p class="text-purple-100">Crafting strong, sticky code so your web works perfectly for everyone.</p>
                    </div>
                </li>
                <li class="flex items-start gap-6">
                    <div class="flex-shrink-0">
                        <svg width="36" height="34" viewBox="0 0 38 33"><ellipse cx="19" cy="24" rx="7" ry="8" fill="#2a133f"/><ellipse cx="19" cy="15" rx="3" ry="3.7" fill="#2a133f"/><ellipse cx="17.8" cy="14.2" rx="0.5" ry="1" fill="#fff"/><ellipse cx="20.2" cy="14.2" rx="0.5" ry="1" fill="#fff"/><path d="M9 16 Q7 10 17 12" stroke="#08000f" stroke-width="1.3"/><path d="M29 16 Q31 10 21 12" stroke="#08000f" stroke-width="1.3"/></svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-purple-200 mb-1">Launch & Optimization</h3>
                        <p class="text-purple-100">Let loose the web and tweak it for ever-better prey capture.</p>
                    </div>
                </li>
            </ol>
        </div>
    </section>
    <!-- TESTIMONIALS Section: clients as prey caught in web -->
    <section class="web-section py-20 bg-gradient-to-br from-[#2e1144] via-[#150d28] to-[#2d003e] relative overflow-hidden">
        <svg class="absolute left-0 top-1/3 opacity-11 pointer-events-none" width="360" height="260" viewBox="0 0 300 200"><circle cx="180" cy="100" r="118" stroke="#be84fc" stroke-width="1.6" opacity="0.12"/></svg>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-black text-white mb-4">Prey We've Caught</h2>
                <p class="text-xl text-purple-200 max-w-2xl mx-auto">Don't take our word: listen to those we've caught<br><span class="italic text-orange-300">(And made very happy!)</span></p>
            </div>
            <div class="flex overflow-x-auto pb-6 snap-x snap-mandatory gap-4 scrollbar-hide testimonials-slider">
                <div class="min-w-full md:min-w-[33.333%] px-4 snap-center group relative">
                    <div class="bg-white/10 rounded-xl p-8 h-full border border-purple-200/20 relative backdrop-blur-md z-10 flex flex-col gap-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-spider text-orange-400 text-xl mr-2"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                        </div>
                        <p>"Caught in SpiderWeb, our conversion is up 40%. A masterful trap for our ideal clients!"</p>
                        <div class="flex items-center gap-3 mt-2">
                            <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center border-2 border-purple-300">
                                <span class="font-bold">JD</span>
                            </div>
                            <div>
                                <h4 class="font-bold">John Doe</h4>
                                <p class="text-purple-100 text-sm">Financial Consultant</p>
                            </div>
                        </div>
                        <!-- Web overlay catching the avatar -->
                        <svg class="absolute left-1 top-1 w-12 h-12 opacity-40 pointer-events-none" viewBox="0 0 50 50"><circle cx="25" cy="25" r="22" stroke="#be84fc" stroke-width="1.4"/></svg>
                    </div>
                </div>

                <div class="min-w-full md:min-w-[33.333%] px-4 snap-center group relative">
                    <div class="bg-white/10 rounded-xl p-8 h-full border border-purple-200/20 relative backdrop-blur-md flex flex-col gap-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-spider text-orange-400 text-xl mr-2"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                        </div>
                        <p>“Our therapy practice’s web now catches tons of new clients — all spun by SpiderWeb’s expert spiders!”</p>
                        <div class="flex items-center gap-3 mt-2">
                            <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center border-2 border-orange-300">
                                <span class="font-bold">AS</span>
                            </div>
                            <div>
                                <h4 class="font-bold">Amanda Smith</h4>
                                <p class="text-purple-100 text-sm">Mental Health Practice</p>
                            </div>
                        </div>
                        <svg class="absolute left-1 top-1 w-12 h-12 opacity-40 pointer-events-none" viewBox="0 0 50 50"><circle cx="25" cy="25" r="22" stroke="#be84fc" stroke-width="1.4"/></svg>
                    </div>
                </div>

                <div class="min-w-full md:min-w-[33.333%] px-4 snap-center group relative">
                    <div class="bg-white/10 rounded-xl p-8 h-full border border-purple-200/20 relative backdrop-blur-md flex flex-col gap-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-spider text-orange-400 text-xl mr-2"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                        </div>
                        <p>"Our cleaning business is catching more high-value prey than ever before. Our new SpiderWeb site works perfectly!"</p>
                        <div class="flex items-center gap-3 mt-2">
                            <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center border-2 border-purple-300">
                                <span class="font-bold">RJ</span>
                            </div>
                            <div>
                                <h4 class="font-bold">Robert Johnson</h4>
                                <p class="text-purple-100 text-sm">Cleaning Services</p>
                            </div>
                        </div>
                        <svg class="absolute left-1 top-1 w-12 h-12 opacity-40 pointer-events-none" viewBox="0 0 50 50"><circle cx="25" cy="25" r="22" stroke="#be84fc" stroke-width="1.4"/></svg>
                    </div>
                </div>

                <div class="min-w-full md:min-w-[33.333%] px-4 snap-center group relative">
                    <div class="bg-white/10 rounded-xl p-8 h-full border border-purple-200/20 relative backdrop-blur-md flex flex-col gap-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-spider text-orange-400 text-xl mr-2"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                            <i class="fas fa-star text-yellow-300"></i>
                        </div>
                        <p>"SpiderWeb’s web for our legal practice catches only the right clients. No more time wasted with the wrong prey."</p>
                        <div class="flex items-center gap-3 mt-2">
                            <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center border-2 border-orange-300">
                                <span class="font-bold">ML</span>
                            </div>
                            <div>
                                <h4 class="font-bold">Maria Lopez</h4>
                                <p class="text-purple-100 text-sm">Legal Practice</p>
                            </div>
                        </div>
                        <svg class="absolute left-1 top-1 w-12 h-12 opacity-40 pointer-events-none" viewBox="0 0 50 50"><circle cx="25" cy="25" r="22" stroke="#be84fc" stroke-width="1.4"/></svg>
                    </div>
                </div>
            </div>
            <div class="flex justify-center mt-6 gap-2">
                <button class="testimonial-dot active w-3 h-3 rounded-full bg-white bg-opacity-30 hover:bg-opacity-100 transition" data-index="0"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-white bg-opacity-30 hover:bg-opacity-100 transition" data-index="1"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-white bg-opacity-30 hover:bg-opacity-100 transition" data-index="2"></button>
                <button class="testimonial-dot w-3 h-3 rounded-full bg-white bg-opacity-30 hover:bg-opacity-100 transition" data-index="3"></button>
            </div>
        </div>
    </section>

    <!-- CTA Web -->
    <section class="py-20 bg-gradient-to-br from-[#191336] via-[#261b3d] to-[#1a0033] relative">
        <svg class="absolute top-0 left-0 w-full h-56 opacity-15 pointer-events-none" fill="none" viewBox="0 0 1000 280">
            <ellipse cx="600" cy="70" rx="400" ry="46" stroke="#be84fc" stroke-width="2"/>
        </svg>
        <div class="container mx-auto px-6 text-center relative z-10">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-3xl md:text-4xl font-black text-white mb-6">Want to Ensnare More Clients?</h2>
                <p class="text-xl text-purple-200 mb-8">Let us weave an alluring web.<br>Let your prey come to you.</p>
                <a href="#contact-form" class="spider-btn inline-block font-bold py-4 px-8 rounded-lg mt-4">Yes, Catch My Prey!</a>

                <!-- Spider on a single thread dropping from above -->
                <div class="absolute left-1/2 -top-14 transform -translate-x-1/2 pointer-events-none">
                    <svg width="34" height="90" viewBox="0 0 34 90"><line x1="17" y1="0" x2="17" y2="28" stroke="#be84fc" stroke-width="2"/><ellipse cx="17" cy="54" rx="13" ry="20" fill="#ef642c" stroke="#ad4300" stroke-width="2"/><ellipse cx="17" cy="34" rx="6.5" ry="6.6" fill="#ef642c" stroke="#ad4300" stroke-width="1.8" />
                    <ellipse cx="15.8" cy="33.5" rx="0.6" ry="0.9" fill="#fff"/>
                    <ellipse cx="18.2" cy="33.5" rx="0.6" ry="0.9" fill="#fff"/>
                    <path d="M9 41 Q7 29 15 33" stroke="#fff" stroke-width="1.2" stroke-linecap="round"/>
                    <path d="M25 41 Q27 29 23 33" stroke="#fff" stroke-width="1.2" stroke-linecap="round"/>
                    <!-- Legs -->
                    <path d="M4 42 Q-2 30 11 38" stroke="#ad4300" stroke-width="1.9" stroke-linecap="round"/>
                    <path d="M3 55 Q-1 52 14 49" stroke="#ad4300" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M6 69 Q1 75 18 63" stroke="#ad4300" stroke-width="1.2" stroke-linecap="round"/>
                    <path d="M11 80 Q7 85 22 71" stroke="#ad4300" stroke-width="1" stroke-linecap="round"/>
                    <path d="M30 42 Q35 30 21 38" stroke="#ad4300" stroke-width="1.9" stroke-linecap="round"/>
                    <path d="M31 55 Q33 52 20 49" stroke="#ad4300" stroke-width="1.5" stroke-linecap="round"/>
                    <path d="M28 69 Q32 75 16 63" stroke="#ad4300" stroke-width="1.2" stroke-linecap="round"/>
                    <path d="M23 80 Q25 85 18 71" stroke="#ad4300" stroke-width="1" stroke-linecap="round"/>
                </svg>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer with subtle web -->
    <footer class="bg-[#1a1025] text-white py-12 relative web-section">
        <svg class="absolute top-0 left-0 h-24 w-96 opacity-35 pointer-events-none" viewBox="0 0 500 90">
            <ellipse cx="12" cy="8" rx="70" ry="24" stroke="#be84fc" stroke-width="2"/>
            <ellipse cx="85" cy="30" rx="40" ry="13" stroke="#be84fc" stroke-width="1"/>
        </svg>
        <div class="container mx-auto px-6 z-10 relative">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <div>
                    <div class="flex items-center mb-4">
                        <!-- Logo spider again for footer -->
                        <svg width="27" height="27" viewBox="0 0 27 27">
                            <!-- No web line for less clutter -->
                            <ellipse cx="13.5" cy="18" rx="5.5" ry="7" fill="#ef642c" stroke="#ad4300" stroke-width="1.2"/>
                            <ellipse cx="13.5" cy="10.5" rx="3.5" ry="3.3" fill="#ef642c" stroke="#ad4300" stroke-width="1"/>
                            <ellipse cx="12.8" cy="10" rx="0.6" ry="0.9" fill="#fff"/>
                            <ellipse cx="14.2" cy="10" rx="0.6" ry="0.9" fill="#fff"/>
                            <!-- Legs -->
                            <path d="M6.3 8 Q2.5 3 9.4 9" stroke="#ad4300" stroke-width="0.7" stroke-linecap="round"/>
                            <path d="M5.4 14 Q1 13 11 14" stroke="#ad4300" stroke-width="0.5" stroke-linecap="round"/>
                            <path d="M6 20 Q2 25 13 20" stroke="#ad4300" stroke-width="0.45" stroke-linecap="round"/>
                            <path d="M9 25 Q7 27 15 21" stroke="#ad4300" stroke-width="0.42" stroke-linecap="round"/>
                            <path d="M20.7 8 Q24.5 3 17.6 9" stroke="#ad4300" stroke-width="0.7" stroke-linecap="round"/>
                            <path d="M21.6 14 Q26 13 16 14" stroke="#ad4300" stroke-width="0.5" stroke-linecap="round"/>
                            <path d="M21 20 Q25 25 14 20" stroke="#ad4300" stroke-width="0.45" stroke-linecap="round"/>
                            <path d="M18 25 Q20 27 12 21" stroke="#ad4300" stroke-width="0.42" stroke-linecap="round"/>
                        </svg>
                        <h3 class="text-xl font-black ml-2">SpiderWeb</h3>
                    </div>
                    <p class="text-purple-100">We spin strategic, beautiful, and <span class="text-orange-300 font-bold">sticky</span> webs for businesses like yours.</p>
                </div>
                <div>
                    <h4 class="text-lg font-semibold mb-4 text-purple-200">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#services" class="text-purple-100 hover:text-orange-300 transition">Web Services</a></li>
                        <li><a href="#process" class="text-purple-100 hover:text-orange-300 transition">Process</a></li>
                        <li><a href="#portfolio" class="text-purple-100 hover:text-orange-300 transition">Portfolio</a></li>
                        <li><a href="#contact-form" class="text-purple-100 hover:text-orange-300 transition">Get Caught</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4 text-purple-200">Contact</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="fas fa-envelope text-purple-400 mr-2"></i>
                            <span class="text-purple-100"><EMAIL></span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone-alt text-purple-400 mr-2"></i>
                            <span class="text-purple-100">(*************</span>
                        </li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-semibold mb-4 text-purple-200">Connect With Us</h4>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 rounded-full bg-[#261536] flex items-center justify-center text-purple-400 hover:bg-[#be84fc] hover:text-white transition">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-[#261536] flex items-center justify-center text-purple-400 hover:bg-[#be84fc] hover:text-white transition">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-[#261536] flex items-center justify-center text-purple-400 hover:bg-[#be84fc] hover:text-white transition">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 rounded-full bg-[#261536] flex items-center justify-center text-purple-400 hover:bg-[#be84fc] hover:text-white transition">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="border-t border-purple-800 pt-8 text-center text-purple-200">
                <p>&copy; 2023 SpiderWeb. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Testimonials: carousel/dots
        document.querySelectorAll('.testimonial-dot').forEach((dot, idx) => {
            dot.addEventListener('click', () => {
                const slider = document.querySelector('.testimonials-slider');
                const slideWidth = slider.querySelector('div').offsetWidth;

                slider.scrollTo({
                    left: slideWidth * idx,
                    behavior: 'smooth'
                });

                // Update active dot
                document.querySelectorAll('.testimonial-dot').forEach(d => d.classList.remove('active', 'bg-opacity-100'));
                dot.classList.add('active', 'bg-opacity-100');
            });
        });
        document.querySelector('.testimonials-slider').addEventListener('scroll', function() {
            const slideWidth = this.querySelector('div').offsetWidth;
            const pos = this.scrollLeft;
            const idx = Math.round(pos / slideWidth);

            // Update active dot
            document.querySelectorAll('.testimonial-dot').forEach(d => d.classList.remove('active', 'bg-opacity-100'));
            if (document.querySelectorAll('.testimonial-dot')[idx]) {
                document.querySelectorAll('.testimonial-dot')[idx].classList.add('active', 'bg-opacity-100');
            }
        });

        // Form submission animation
        document.getElementById('projectForm')?.addEventListener('submit', function(e) {
            e.preventDefault();
            const btn = this.querySelector('button[type="submit"]');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
            btn.disabled = true;
            setTimeout(() => {
                this.style.display = 'none';
                const msg = document.getElementById('successMessage');
                msg.classList.remove('hidden');
                msg.classList.add('fade-in');
                msg.scrollIntoView({behavior:'smooth'});
            }, 1300);
        });

        // Smooth scroll for anchor links (top offset for sticky nav)
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const targetId = this.getAttribute('href');
                if (targetId === '#' || !document.querySelector(targetId)) return;

                e.preventDefault();
                const topOffset = 90;
                const targetEl = document.querySelector(targetId);
                window.scrollTo({ top: targetEl.offsetTop - topOffset, behavior: 'smooth' });
            });
        });

        // Fade in effect for elements with .fade-in as they come into view
        function fadeInOnScroll(){
            document.querySelectorAll('.fade-in').forEach(el=>{
                const rect = el.getBoundingClientRect();
                if(rect.top < window.innerHeight-80) el.style.opacity=1;
            });
        }
        window.addEventListener('scroll', fadeInOnScroll);
        window.addEventListener('DOMContentLoaded', fadeInOnScroll);

        // Animated services cards slight hover
        document.querySelectorAll('.bg-[#003366]/90').forEach(card=>{
            card.addEventListener('mouseenter',()=>card.style.transform='translateY(-6px) scale(1.035)');
            card.addEventListener('mouseleave',()=>card.style.transform='');
        });
    </script>
</body>
</html>